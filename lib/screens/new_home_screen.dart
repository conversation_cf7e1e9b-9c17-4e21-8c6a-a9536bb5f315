import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../providers/user_profile_provider.dart';
import '../providers/theme_provider.dart';
import '../constants/app_text_styles.dart';
import '../constants/modern_colors.dart';
import '../constants/app_spacing.dart';
import '../widgets/safe_text.dart';
import '../widgets/user_info_widget.dart';
import '../l10n/app_localizations.dart';
import '../widgets/welcome_dialog.dart';

import 'saved_videos_screen.dart';
import '../models/user_profile.dart';
import 'meal_suggestion_screen.dart';
import 'weekly_menu_screen.dart';
import 'settings_screen.dart';
import 'user_profile_screen.dart';



/// Màn hình chính mới với focus vào gợi ý món ăn
class NewHomeScreen extends StatefulWidget {
  static const String routeName = '/new-home';

  const NewHomeScreen({super.key});

  @override
  State<NewHomeScreen> createState() => _NewHomeScreenState();
}

class _NewHomeScreenState extends State<NewHomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  /// Lấy avatar URL từ nhiều nguồn theo thứ tự ưu tiên
  Future<String?> _getAvatarUrl(UserProfile? userProfile, User? currentUser) async {
    // 1. Ưu tiên UserProfile.photoUrl (từ database)
    if (userProfile?.photoUrl != null && userProfile!.photoUrl!.isNotEmpty) {
      print('🔍 Using avatar from UserProfile: ${userProfile!.photoUrl}');
      return userProfile!.photoUrl;
    }

    // 2. Thử lấy từ SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedPhotoUrl = prefs.getString('photo_url');
      if (savedPhotoUrl != null && savedPhotoUrl.isNotEmpty) {
        print('🔍 Using avatar from SharedPreferences: $savedPhotoUrl');
        return savedPhotoUrl;
      }
    } catch (e) {
      print('🔍 Error accessing SharedPreferences for avatar: $e');
    }

    // 3. Thử lấy từ Supabase auth metadata
    final authAvatarUrl = currentUser?.userMetadata?['avatar_url'];
    if (authAvatarUrl != null && authAvatarUrl.isNotEmpty) {
      print('🔍 Using avatar from Auth metadata: $authAvatarUrl');
      return authAvatarUrl;
    }

    print('🔍 No avatar found, will use default');
    return null;
  }

  /// Lấy tên hiển thị từ user_profiles database trước, sau đó fallback
  Future<String> _getDisplayNameFromProfile(UserProfile? userProfile, User? currentUser) async {
    // 1. Ưu tiên display_name từ user_profiles database
    if (userProfile?.displayName != null && userProfile!.displayName!.isNotEmpty) {
      final dbDisplayName = userProfile.displayName!.trim();
      print('🔍 Using display_name from database: $dbDisplayName');

      // Nếu là tên đầy đủ, lấy tên cuối
      final nameParts = dbDisplayName.split(' ');
      if (nameParts.isNotEmpty) {
        final name = nameParts.last;
        print('🔍 Extracted name from DB: $name');
        return name;
      }
      return dbDisplayName;
    }

    // 2. Fallback: Lấy từ auth metadata
    final authDisplayName = currentUser?.userMetadata?['full_name'];
    if (authDisplayName != null && authDisplayName.isNotEmpty) {
      print('🔍 Using auth displayName: $authDisplayName');
      final nameParts = authDisplayName.trim().split(' ');
      if (nameParts.isNotEmpty) {
        final name = nameParts.last;
        print('🔍 Extracted name from auth: $name');
        return name;
      }
    }

    // 3. Fallback: Lấy từ SharedPreferences
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedDisplayName = prefs.getString('display_name');
      if (savedDisplayName != null && savedDisplayName.isNotEmpty) {
        print('🔍 Using saved displayName: $savedDisplayName');
        final nameParts = savedDisplayName.trim().split(' ');
        if (nameParts.isNotEmpty) {
          final name = nameParts.last;
          print('🔍 Extracted name from saved: $name');
          return name;
        }
      }
    } catch (e) {
      print('🔍 Error accessing SharedPreferences: $e');
    }

    // 4. Fallback cuối: extract từ email
    if (currentUser?.email != null) {
      final name = _extractNameFromEmail(currentUser!.email!);
      print('🔍 Using name extracted from email: $name');
      return name;
    }

    return 'bạn'; // Will be replaced with localized version in calling method
  }

  /// Lấy tên hiển thị từ nhiều nguồn theo thứ tự ưu tiên (giống user_profile_screen.dart)
  Future<String> _getDisplayName(UserProfile? userProfile, User? currentUser) async {
    // Lấy thông tin từ SharedPreferences
    String? savedDisplayName;
    try {
      final prefs = await SharedPreferences.getInstance();
      savedDisplayName = prefs.getString('display_name');
    } catch (e) {
      print('🔍 Error accessing SharedPreferences: $e');
    }

    // Logic giống user_profile_screen.dart:
    // 1. Ưu tiên auth metadata ['full_name']
    // 2. Sau đó SharedPreferences ['display_name']
    // 3. Cuối cùng fallback
    final authDisplayName = currentUser?.userMetadata?['full_name'];
    final displayName = authDisplayName ?? savedDisplayName ?? 'Người dùng';

    print('🔍 Auth displayName: $authDisplayName');
    print('🔍 Saved displayName: $savedDisplayName');
    print('🔍 Final displayName: $displayName');

    // Lấy tên cuối cùng từ displayName (tên thật)
    if (displayName != 'Người dùng') {
      final nameParts = displayName.trim().split(' ');
      if (nameParts.isNotEmpty) {
        final name = nameParts.last;
        print('🔍 Extracted name: $name');
        return name;
      }
    }

    // Fallback: extract từ email
    if (currentUser?.email != null) {
      final name = _extractNameFromEmail(currentUser!.email!);
      print('🔍 Using name extracted from email: $name');
      return name;
    }

    return 'bạn';
  }

  /// Extract tên từ email address
  /// Ví dụ: "<EMAIL>" -> "Nguyễn Văn A"
  /// "<EMAIL>" -> "John Doe"
  String _extractNameFromEmail(String email) {
    try {
      // Lấy phần trước @ trong email
      final localPart = email.split('@').first;

      // Tách các từ bằng dấu chấm, gạch dưới, hoặc số
      final words = localPart
          .replaceAll(RegExp(r'[._\-0-9]+'), ' ')
          .split(' ')
          .where((word) => word.isNotEmpty)
          .toList();

      if (words.isEmpty) return 'bạn';

      // Capitalize từng từ và ghép lại
      final capitalizedWords = words.map((word) {
        if (word.length == 1) return word.toUpperCase();
        return word[0].toUpperCase() + word.substring(1).toLowerCase();
      }).toList();

      // Nếu có nhiều từ, lấy tên đầu tiên (thường là họ trong tiếng Việt)
      // Hoặc từ cuối cùng (thường là tên trong tiếng Anh)
      if (capitalizedWords.length > 1) {
        // Ưu tiên lấy từ cuối cùng (tên)
        return capitalizedWords.last;
      }

      return capitalizedWords.first;
    } catch (e) {
      print('Lỗi extract tên từ email: $e');
      return 'bạn';
    }
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Hiển thị welcome dialog sau khi build xong
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WelcomeDialog.showIfNeeded(context);
    });
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));
    
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final userProfileProvider = Provider.of<UserProfileProvider>(context);
    final userProfile = userProfileProvider.userProfile;
    
    return Scaffold(
      backgroundColor: isDarkMode
          ? Theme.of(context).colorScheme.surface
          : const Color(0xFFF8F9FA),
      drawer: _buildDrawer(context),
      body: SafeArea(
        child: FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: CustomScrollView(
              slivers: [
                // App Bar
                SliverAppBar(
                  expandedHeight: 60,
                  floating: false,
                  pinned: true,
                  backgroundColor: isDarkMode
                      ? Theme.of(context).colorScheme.surfaceContainerHigh
                      : Colors.white,
                  elevation: 0,
                  flexibleSpace: FlexibleSpaceBar(
                    title: SafeText(
                      'CookSpark AI',
                      style: AppTextStyles.title2(context).copyWith(
                        color: isDarkMode
                            ? Theme.of(context).colorScheme.primary
                            : Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        shadows: isDarkMode ? [
                          Shadow(
                            color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ] : null,
                      ),
                    ),
                    centerTitle: false,
                    titlePadding: const EdgeInsets.only(left: 60, bottom: 5),
                  ),
                  actions: [
                    Padding(
                      padding: const EdgeInsets.only(right: 8.0),
                      child: GestureDetector(
                        onTap: () {
                          Navigator.of(context).pushNamed(UserProfileScreen.routeName);
                        },
                        child: FutureBuilder<String?>(
                          future: _getAvatarUrl(userProfile, Supabase.instance.client.auth.currentUser),
                          builder: (context, snapshot) {
                            final avatarUrl = snapshot.data;
                            return CircleAvatar(
                              radius: 20,
                              backgroundColor: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                              backgroundImage: avatarUrl != null && avatarUrl.isNotEmpty
                                  ? NetworkImage(avatarUrl)
                                  : null,
                              child: avatarUrl == null || avatarUrl.isEmpty
                                  ? Icon(
                                      Icons.person,
                                      color: Theme.of(context).primaryColor,
                                      size: 48,
                                    )
                                  : null,
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
                
                // Main content
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Greeting
                        FutureBuilder<Widget>(
                          future: _buildGreeting(context, userProfile, isDarkMode),
                          builder: (context, snapshot) {
                            if (snapshot.hasData) {
                              return snapshot.data!;
                            }
                            // Loading placeholder
                            return Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                    Theme.of(context).primaryColor.withValues(alpha: 0.05),
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                border: Border.all(
                                  color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
                                ),
                              ),
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          },
                        ),
                        const SizedBox(height: 32),

                        // Main suggestion button
                        _buildMainSuggestionButton(context, isDarkMode),
                        const SizedBox(height: 32),
                        
                        // Quick actions
                        _buildQuickActions(context, isDarkMode),
                        const SizedBox(height: 32),
                        

                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<Widget> _buildGreeting(BuildContext context, userProfile, bool isDarkMode) async {
    final l10n = AppLocalizations.of(context);
    final now = DateTime.now();
    String greeting;
    String emoji;
    String timeMessage;

    if (now.hour < 12) {
      greeting = l10n?.goodMorning ?? 'Chào buổi sáng';
      emoji = '🔆';
      timeMessage = l10n?.morningMessage ?? 'Hãy bắt đầu ngày mới với một bữa sáng ngon miệng!';
    } else if (now.hour < 18) {
      greeting = l10n?.goodAfternoon ?? 'Chào buổi chiều';
      emoji = '☀️';
      timeMessage = l10n?.afternoonMessage ?? 'Đã đến giờ nghỉ trưa rồi, bạn muốn ăn gì?';
    } else {
      greeting = l10n?.goodEvening ?? 'Chào buổi tối';
      emoji = '🌙';
      timeMessage = l10n?.eveningMessage ?? 'Thời gian cho bữa tối ấm cúng cùng \n gia đình!';
    }

    // Debug userProfile
    print('🔍 UserProfile debug: $userProfile');
    print('🔍 UserProfile gender: ${userProfile?.gender}');

    // Nếu userProfile null, thử load trực tiếp từ database
    UserProfile? actualUserProfile = userProfile;
    if (actualUserProfile == null) {
      print('🔍 UserProfile is null, trying to load from database...');
      try {
        final currentUser = Supabase.instance.client.auth.currentUser;
        if (currentUser != null) {
          final response = await Supabase.instance.client
              .from('user_profiles')
              .select()
              .eq('user_id', currentUser.id)
              .single();

          actualUserProfile = UserProfile.fromJson(response);
          print('🔍 Loaded UserProfile from database: ${actualUserProfile.gender}');
        }
      } catch (e) {
        print('🔍 Error loading UserProfile from database: $e');
      }
    }

    // Lấy tên và xưng hô phù hợp
    String name = l10n?.you ?? 'bạn';
    String title = '';

    // Lấy tên từ nhiều nguồn theo thứ tự ưu tiên
    final currentUser = Supabase.instance.client.auth.currentUser;
    name = await _getDisplayNameFromProfile(actualUserProfile, currentUser);
    print('🔍 Final display name: $name');

    // Thêm xưng hô dựa trên giới tính (nếu có thông tin)
    if (actualUserProfile?.gender != null) {
      final gender = actualUserProfile!.gender!;
      print('🔍 Gender found: $gender');
      switch (gender) {
        case Gender.male:
          title = l10n?.brother ?? 'anh ';
          print('🔍 Using male title: $title');
          break;
        case Gender.female:
          title = l10n?.sister ?? 'chị ';
          print('🔍 Using female title: $title');
          break;
        case Gender.other:
          title = '';
          print('🔍 Using other title: $title');
          break;
      }
    } else {
      print('🔍 No gender information found');
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Theme.of(context).primaryColor.withValues(alpha: 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SafeText(
                emoji,
                style: const TextStyle(fontSize: 32),
              ),
              AppSpacing.hSpaceMD,
              Expanded(
                child: Builder(
                  builder: (context) {
                    final fullText = '$greeting, ${title.trim()}\u00A0$name!';
                    print('🔍 Debug greeting text: "$fullText"');
                    print('🔍 Greeting: "$greeting"');
                    print('🔍 Title: "$title"');
                    print('🔍 Name: "$name"');
                    print('🔍 Has non-breaking space: ${fullText.contains('\u00A0')}');

                    return RichText(
                      maxLines: 2,
                      overflow: TextOverflow.visible,
                      softWrap: true,
                      text: TextSpan(
                        style: AppTextStyles.title2(context).copyWith(
                          color: isDarkMode ? Colors.white : Colors.black87,
                          fontWeight: FontWeight.w600,
                        ),
                        children: [
                          TextSpan(text: '$greeting, '),
                          // Sử dụng WidgetSpan để đảm bảo title + name không bao giờ bị tách
                          WidgetSpan(
                            child: Text(
                              '${title.trim()} $name!',
                              style: AppTextStyles.title2(context).copyWith(
                                color: isDarkMode ? Colors.white : Colors.black87,
                                fontWeight: FontWeight.w600,
                                wordSpacing: 0,
                                letterSpacing: 0,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.visible,
                              softWrap: false, // Không cho phép xuống dòng trong widget này
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
          AppSpacing.spaceSM,
          SizedBox(
            width: double.infinity,
            child: Text(
              timeMessage,
              style: AppTextStyles.body(context).copyWith(
                color: isDarkMode ? Colors.white70 : Colors.grey[600],
                height: 1.4,
              ),
              maxLines: 4,
              overflow: TextOverflow.visible,
              softWrap: true,
              textAlign: TextAlign.left,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainSuggestionButton(BuildContext context, bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    return Container(
      width: double.infinity,
      height: 140,
      decoration: BoxDecoration(
        gradient: isDarkMode
            ? ModernColors.getModernGradient(
                Provider.of<ThemeProvider>(context).getActiveColor(),
                isDark: true
              )
            : ModernColors.getModernGradient(Theme.of(context).primaryColor),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          if (isDarkMode)
            ModernColors.getGlowEffect(
              Provider.of<ThemeProvider>(context).getActiveColor(),
              intensity: 0.4
            )
          else
            BoxShadow(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            Navigator.of(context).pushNamed(MealSuggestionScreen.routeName);
          },
          borderRadius: BorderRadius.circular(20),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SafeText(
                        l10n?.mealSuggestion ?? 'Gợi ý món ăn',
                        style: AppTextStyles.title2(context).copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Flexible(
                        child: Text(
                          l10n?.aiWillHelpYouFind ?? 'AI sẽ giúp bạn tìm món ăn phù hợp',
                          style: AppTextStyles.caption1(context).copyWith(
                            color: Colors.white.withValues(alpha: 0.9),
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.visible,
                          softWrap: true,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context, bool isDarkMode) {
    final l10n = AppLocalizations.of(context);
    final actions = [
      {
        'title': l10n?.savedDishes ?? 'Món ăn đã lưu',
        'subtitle': l10n?.savedVideos ?? 'Video đã lưu',
        'icon': Icons.bookmark,
        'route': 'saved_videos', // Tạm thời dùng route đặc biệt
      },
      {
        'title': l10n?.weeklyMenu ?? 'Thực đơn tuần',
        'subtitle': l10n?.viewMealPlan ?? 'Xem kế hoạch bữa ăn',
        'icon': Icons.calendar_today,
        'route': WeeklyMenuScreen.routeName,
      },
      {
        'title': l10n?.settings ?? 'Cài đặt',
        'subtitle': l10n?.customizeApp ?? 'Tùy chỉnh ứng dụng',
        'icon': Icons.settings,
        'route': SettingsScreen.routeName,
      },
      {
        'title': l10n?.personalInfo ?? 'Hồ sơ',
        'subtitle': l10n?.personalInformation ?? 'Thông tin cá nhân',
        'icon': Icons.person,
        'route': UserProfileScreen.routeName,
      },
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SafeText(
          l10n?.quickAccess ?? 'Truy cập nhanh',
          style: AppTextStyles.title3(context).copyWith(
            color: isDarkMode
                ? Theme.of(context).colorScheme.onSurface
                : Colors.black87,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        // Hàng đầu tiên - 2 cột
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 6),
                  child: _buildQuickActionCard(
                    context,
                    actions[0]['title'] as String,
                    actions[0]['subtitle'] as String,
                    actions[0]['icon'] as IconData,
                    actions[0]['route'] as String,
                    isDarkMode,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 6),
                  child: _buildQuickActionCard(
                    context,
                    actions[1]['title'] as String,
                    actions[1]['subtitle'] as String,
                    actions[1]['icon'] as IconData,
                    actions[1]['route'] as String,
                    isDarkMode,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),
        // Hàng thứ hai - 2 cột
        IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(right: 6),
                  child: _buildQuickActionCard(
                    context,
                    actions[2]['title'] as String,
                    actions[2]['subtitle'] as String,
                    actions[2]['icon'] as IconData,
                    actions[2]['route'] as String,
                    isDarkMode,
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(left: 6),
                  child: _buildQuickActionCard(
                    context,
                    actions[3]['title'] as String,
                    actions[3]['subtitle'] as String,
                    actions[3]['icon'] as IconData,
                    actions[3]['route'] as String,
                    isDarkMode,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    String route,
    bool isDarkMode,
  ) {
    return Container(
      constraints: const BoxConstraints(
        minHeight: 100,
        maxHeight: 140,
      ),
      decoration: BoxDecoration(
        gradient: isDarkMode
            ? LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Theme.of(context).colorScheme.surfaceContainerHigh,
                  Theme.of(context).colorScheme.surfaceContainer,
                ],
              )
            : null,
        color: isDarkMode ? null : Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: isDarkMode
            ? Border.all(
                color: Theme.of(context).colorScheme.outline.withValues(alpha: 0.3),
                width: 1,
              )
            : null,
        boxShadow: [
          BoxShadow(
            color: isDarkMode
                ? Colors.black.withValues(alpha: 0.2)
                : Colors.grey.withValues(alpha: 0.1),
            blurRadius: isDarkMode ? 8 : 10,
            offset: const Offset(0, 4),
          ),
          if (isDarkMode)
            BoxShadow(
              color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.05),
              blurRadius: 20,
              offset: const Offset(0, 0),
            ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            if (route == 'saved_videos') {
              Navigator.of(context).pushNamed(SavedVideosScreen.routeName);
            } else {
              Navigator.of(context).pushNamed(route);
            }
          },
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isDarkMode
                        ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.15)
                        : Theme.of(context).primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    icon,
                    color: isDarkMode
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).primaryColor,
                    size: 20,
                  ),
                ),
                AppSpacing.spaceXS,
                Flexible(
                  child: SafeText(
                    title,
                    style: AppTextStyles.subhead(context).copyWith(
                      color: isDarkMode
                          ? Theme.of(context).colorScheme.onSurface
                          : Colors.black87,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.visible,
                  ),
                ),
                AppSpacing.spaceXS,
                Flexible(
                  child: SafeText(
                    subtitle,
                    style: AppTextStyles.footnote(context).copyWith(
                      color: isDarkMode
                          ? Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.7)
                          : Colors.grey[600],
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.visible,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }



  Widget _buildDrawer(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          const UserInfoWidget(),
          ListTile(
            leading: const Icon(Icons.home),
            title: SafeText(l10n?.home ?? 'Trang chủ'),
            onTap: () {
              Navigator.pop(context);
            },
          ),
          ListTile(
            leading: const Icon(Icons.auto_awesome),
            title: SafeText(l10n?.mealSuggestion ?? 'Gợi ý món ăn'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed(MealSuggestionScreen.routeName);
            },
          ),
          ListTile(
            leading: const Icon(Icons.calendar_today),
            title: SafeText(l10n?.weeklyMenu ?? 'Thực đơn tuần'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed(WeeklyMenuScreen.routeName);
            },
          ),
          const Divider(),


          ListTile(
            leading: const Icon(Icons.person),
            title: SafeText(l10n?.personalInformation ?? 'Thông tin cá nhân'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed(UserProfileScreen.routeName);
            },
          ),
          ListTile(
            leading: const Icon(Icons.settings),
            title: SafeText(l10n?.settings ?? 'Cài đặt'),
            onTap: () {
              Navigator.pop(context);
              Navigator.of(context).pushNamed(SettingsScreen.routeName);
            },
          ),
        ],
      ),
    );
  }
}
